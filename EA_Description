Absolutely! Below is a full MVP-format description of a **profitable trading strategy** using **5 Moving Averages and Fractals**, designed for implementation in an MT5 Expert Advisor. This strategy balances confirmation, precision, and trend-following logic, and includes SL/TP placement based on indicator signals.

---

## ✅ MVP FORMAT – **5-MA + Fractal Confirmation Strategy**

---

### 🧠 **Objective**

To create a robust, trend-following and reversal-trading strategy using 5 Moving Averages (MAs) of varying lengths and Fractals to:

* Detect high-probability **buy/sell entries**
* Determine **stop loss** and **take profit** based on recent price action
* Maintain controlled risk with clean trend filters and dynamic SL/TP logic

---

### 🧰 **Tools & Indicators Used**

* **5 Moving Averages**:

  * MA1: 9 EMA – Fast signal
  * MA2: 21 EMA – Momentum
  * MA3: 50 SMA – Short-term trend
  * MA4: 100 SMA – Medium trend
  * MA5: 200 SMA – Long-term trend
* **Fractals**:

  * Used to detect key swing highs/lows for SL and TP anchoring

---

### 💡 **Core Strategy Logic**

#### **Buy Conditions:**

1. Price is **above all 5 MAs** (trend confirmation)
2. MAs are aligned in order: `EMA(9) > EMA(21) > SMA(50) > SMA(100) > SMA(200)`
3. A **bullish fractal appears** (lowest low with higher lows on both sides)
4. **Entry:** After candle closes above bullish fractal and EMA(9)
5. **Stop Loss:** Set just below the **latest bearish fractal** (swing low)
6. **Take Profit:**

   * Option 1: Fixed risk-reward (e.g., 1:2)
   * Option 2: TP at previous swing high (nearest fractal top)

#### **Sell Conditions:**

1. Price is **below all 5 MAs**
2. MAs are aligned in reverse: `EMA(9) < EMA(21) < SMA(50) < SMA(100) < SMA(200)`
3. A **bearish fractal appears** (highest high with lower highs on both sides)
4. **Entry:** After candle closes below bearish fractal and EMA(9)
5. **Stop Loss:** Just above the latest bullish fractal (swing high)
6. **Take Profit:**

   * Option 1: Fixed risk-reward (e.g., 1:2)
   * Option 2: TP at previous swing low (nearest fractal bottom)

---

### ⚙️ **Additional Controls (User Inputs)**

* `UseFixedSLTP = true/false`
* `RiskPercent = X%`
* `UseSingleTrade = true/false` → If false, multiple trades allowed based on signal queue
* `MaxOpenTrades = N`
* `MA_periods = [9, 21, 50, 100, 200]` (editable)
* `TrailingStopEnabled = true/false`
* `TrendFilterEnabled = true/false`

---

### 🎯 **Risk Management Options**

* **Fixed Lot or Percent-based Lot Sizing**
* **SL from fractal swing + buffer**
* **TP as fixed R\:R (configurable) or next key fractal**
* **Daily max loss filter** (optional)

---

### 📈 Trade Management

* Optional **trailing stop** can be activated when trade is in profit above 1×SL.
* Optional **breakeven** trigger after X pips or 1×SL

---

### 📊 **Strategy Type**

* **Trend-following strategy** with fractal-based precision
* Can handle **one or multiple trades** (configurable)
* High-confidence setup using **indicator confluence**

---

### 🔍 Strengths

* Works on **Gold (XAUUSD)**, major forex pairs, and indices
* Avoids ranging markets with MA filter
* Fractal SL/TP increases logic around market structure

---

### ⚠️ Challenges & Considerations

* Works best on **H1 or H4 timeframes**
* Avoid during high-impact news without a news filter
* Best with **low spread ECN brokers**

---

### 🚀 Why This Stands Out

* Combines **multi-timeframe market structure (fractals)** with **momentum and trend confluence (5 MAs)**
* Dynamic, smart SL/TP logic that evolves with market context
* Fully configurable for your trading style (conservative/aggressive)

---

Would you like me to generate the MQL5 code structure or indicator setup next?
