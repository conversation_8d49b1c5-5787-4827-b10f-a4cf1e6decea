//+------------------------------------------------------------------+
//|                                                5MA_Fractal_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "=== Moving Average Settings ==="
input int MA1_Period = 9;          // EMA 9 Period
input int MA2_Period = 21;         // EMA 21 Period  
input int MA3_Period = 50;         // SMA 50 Period
input int MA4_Period = 100;        // SMA 100 Period
input int MA5_Period = 200;        // SMA 200 Period

input group "=== Trading Settings ==="
input double RiskPercent = 2.0;    // Risk Percent per Trade
input bool UseFixedSLTP = false;   // Use Fixed SL/TP
input double FixedSL = 50;         // Fixed Stop Loss (pips)
input double FixedTP = 100;        // Fixed Take Profit (pips)
input double RiskReward = 2.0;     // Risk:Reward Ratio
input bool UseSingleTrade = true;  // Allow Only One Trade
input int MaxOpenTrades = 3;       // Maximum Open Trades
input bool TrailingStopEnabled = true; // Enable Trailing Stop
input bool TrendFilterEnabled = true;  // Enable Trend Filter

input group "=== Risk Management ==="
input double MaxDailyLoss = 5.0;   // Max Daily Loss (%)
input bool UseBreakeven = true;    // Move to Breakeven
input double BreakevenPips = 20;   // Breakeven Trigger (pips)

input group "=== General Settings ==="
input int MagicNumber = 123456;    // Magic Number
input string TradeComment = "5MA_Fractal"; // Trade Comment

//--- Global variables
int ma1_handle, ma2_handle, ma3_handle, ma4_handle, ma5_handle;
int fractal_handle;
double ma1[], ma2[], ma3[], ma4[], ma5[];
double fractal_up[], fractal_down[];
datetime last_trade_time = 0;
double daily_loss = 0;
datetime current_day = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Create MA handles
   ma1_handle = iMA(_Symbol, PERIOD_CURRENT, MA1_Period, 0, MODE_EMA, PRICE_CLOSE);
   ma2_handle = iMA(_Symbol, PERIOD_CURRENT, MA2_Period, 0, MODE_EMA, PRICE_CLOSE);
   ma3_handle = iMA(_Symbol, PERIOD_CURRENT, MA3_Period, 0, MODE_SMA, PRICE_CLOSE);
   ma4_handle = iMA(_Symbol, PERIOD_CURRENT, MA4_Period, 0, MODE_SMA, PRICE_CLOSE);
   ma5_handle = iMA(_Symbol, PERIOD_CURRENT, MA5_Period, 0, MODE_SMA, PRICE_CLOSE);
   
   //--- Create Fractal handle
   fractal_handle = iFractals(_Symbol, PERIOD_CURRENT);
   
   //--- Check handles
   if(ma1_handle == INVALID_HANDLE || ma2_handle == INVALID_HANDLE || 
      ma3_handle == INVALID_HANDLE || ma4_handle == INVALID_HANDLE || 
      ma5_handle == INVALID_HANDLE || fractal_handle == INVALID_HANDLE)
   {
      Print("Error creating indicators");
      return INIT_FAILED;
   }
   
   //--- Set array as series
   ArraySetAsSeries(ma1, true);
   ArraySetAsSeries(ma2, true);
   ArraySetAsSeries(ma3, true);
   ArraySetAsSeries(ma4, true);
   ArraySetAsSeries(ma5, true);
   ArraySetAsSeries(fractal_up, true);
   ArraySetAsSeries(fractal_down, true);
   
   Print("5MA Fractal EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   IndicatorRelease(ma1_handle);
   IndicatorRelease(ma2_handle);
   IndicatorRelease(ma3_handle);
   IndicatorRelease(ma4_handle);
   IndicatorRelease(ma5_handle);
   IndicatorRelease(fractal_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check for new bar
   static datetime last_bar_time = 0;
   datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   
   if(current_bar_time == last_bar_time)
      return;
   last_bar_time = current_bar_time;
   
   //--- Check daily loss limit
   CheckDailyLoss();
   if(daily_loss >= MaxDailyLoss)
   {
      Print("Daily loss limit reached: ", daily_loss, "%");
      return;
   }
   
   //--- Update indicators
   if(!UpdateIndicators())
      return;
   
   //--- Manage existing trades
   ManageTrades();
   
   //--- Check for new trade signals
   if(CanOpenNewTrade())
   {
      CheckBuySignal();
      CheckSellSignal();
   }
}

//+------------------------------------------------------------------+
//| Update all indicators                                            |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   //--- Copy MA data
   if(CopyBuffer(ma1_handle, 0, 0, 3, ma1) <= 0) return false;
   if(CopyBuffer(ma2_handle, 0, 0, 3, ma2) <= 0) return false;
   if(CopyBuffer(ma3_handle, 0, 0, 3, ma3) <= 0) return false;
   if(CopyBuffer(ma4_handle, 0, 0, 3, ma4) <= 0) return false;
   if(CopyBuffer(ma5_handle, 0, 0, 3, ma5) <= 0) return false;
   
   //--- Copy Fractal data
   if(CopyBuffer(fractal_handle, 0, 0, 10, fractal_up) <= 0) return false;
   if(CopyBuffer(fractal_handle, 1, 0, 10, fractal_down) <= 0) return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if can open new trade                                     |
//+------------------------------------------------------------------+
bool CanOpenNewTrade()
{
   if(UseSingleTrade && PositionsTotal() > 0)
      return false;
      
   if(PositionsTotal() >= MaxOpenTrades)
      return false;
      
   return true;
}

//+------------------------------------------------------------------+
//| Check for buy signal                                            |
//+------------------------------------------------------------------+
void CheckBuySignal()
{
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   //--- Check MA alignment for bullish trend
   if(!IsBullishTrend())
      return;
   
   //--- Check if price is above all MAs
   if(current_price <= ma1[0] || current_price <= ma2[0] || 
      current_price <= ma3[0] || current_price <= ma4[0] || 
      current_price <= ma5[0])
      return;
   
   //--- Find recent bullish fractal
   double fractal_low = FindRecentFractal(false);
   if(fractal_low == 0)
      return;
   
   //--- Check if price closed above fractal and EMA9
   double close_price = iClose(_Symbol, PERIOD_CURRENT, 1);
   if(close_price > fractal_low && close_price > ma1[1])
   {
      //--- Calculate SL and TP
      double sl_price = UseFixedSLTP ? current_price - FixedSL * _Point * 10 : 
                        FindRecentFractal(true) - 10 * _Point;
      double tp_price = UseFixedSLTP ? current_price + FixedTP * _Point * 10 : 
                        current_price + (current_price - sl_price) * RiskReward;
      
      //--- Open buy trade
      OpenTrade(ORDER_TYPE_BUY, current_price, sl_price, tp_price);
   }
}
