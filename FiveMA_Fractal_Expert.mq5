//+------------------------------------------------------------------+
//|                                        FiveMA_Fractal_Expert.mq5 |
//|                        5 Moving Averages + Fractals Expert Advisor |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "5MA Fractal Strategy"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Profitable EA using 5 MAs and Fractals for trend confirmation"

//--- Input parameters
input group "=== Moving Average Settings ==="
input int MA1_Period = 9;          // EMA 9 (Fast Signal)
input int MA2_Period = 21;         // EMA 21 (Momentum)
input int MA3_Period = 50;         // SMA 50 (Short-term Trend)
input int MA4_Period = 100;        // SMA 100 (Medium Trend)
input int MA5_Period = 200;        // SMA 200 (Long-term Trend)

input group "=== Trading Settings ==="
input double RiskPercent = 2.0;    // Risk Percent per Trade
input bool UseFixedSLTP = false;   // Use Fixed SL/TP instead of Fractals
input double FixedSL = 50;         // Fixed Stop Loss (pips)
input double FixedTP = 100;        // Fixed Take Profit (pips)
input double RiskReward = 2.0;     // Risk:Reward Ratio when using fractals
input bool UseSingleTrade = true;  // Allow Only One Trade at a Time
input int MaxOpenTrades = 3;       // Maximum Open Trades
input bool TrailingStopEnabled = true; // Enable Trailing Stop
input bool TrendFilterEnabled = true;  // Enable MA Trend Filter

input group "=== Risk Management ==="
input double MaxDailyLoss = 5.0;   // Max Daily Loss Percentage
input bool UseBreakeven = true;    // Move SL to Breakeven
input double BreakevenPips = 20;   // Breakeven Trigger (pips)
input double TrailingStart = 30;   // Trailing Stop Start (pips)
input double TrailingStep = 10;    // Trailing Stop Step (pips)

input group "=== General Settings ==="
input int MagicNumber = 555777;    // Magic Number
input string TradeComment = "5MA_Fractal_Pro"; // Trade Comment

//--- Global variables
int ma1_handle, ma2_handle, ma3_handle, ma4_handle, ma5_handle;
int fractal_handle;
double ma1[], ma2[], ma3[], ma4[], ma5[];
double fractal_up[], fractal_down[];
datetime last_trade_time = 0;
double daily_loss = 0;
datetime current_day = 0;
double daily_start_balance = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Create Moving Average handles
   ma1_handle = iMA(_Symbol, PERIOD_CURRENT, MA1_Period, 0, MODE_EMA, PRICE_CLOSE);
   ma2_handle = iMA(_Symbol, PERIOD_CURRENT, MA2_Period, 0, MODE_EMA, PRICE_CLOSE);
   ma3_handle = iMA(_Symbol, PERIOD_CURRENT, MA3_Period, 0, MODE_SMA, PRICE_CLOSE);
   ma4_handle = iMA(_Symbol, PERIOD_CURRENT, MA4_Period, 0, MODE_SMA, PRICE_CLOSE);
   ma5_handle = iMA(_Symbol, PERIOD_CURRENT, MA5_Period, 0, MODE_SMA, PRICE_CLOSE);
   
   //--- Create Fractal handle
   fractal_handle = iFractals(_Symbol, PERIOD_CURRENT);
   
   //--- Validate handles
   if(ma1_handle == INVALID_HANDLE || ma2_handle == INVALID_HANDLE || 
      ma3_handle == INVALID_HANDLE || ma4_handle == INVALID_HANDLE || 
      ma5_handle == INVALID_HANDLE || fractal_handle == INVALID_HANDLE)
   {
      Print("❌ Error creating indicators - EA initialization failed");
      return INIT_FAILED;
   }
   
   //--- Set arrays as time series
   ArraySetAsSeries(ma1, true);
   ArraySetAsSeries(ma2, true);
   ArraySetAsSeries(ma3, true);
   ArraySetAsSeries(ma4, true);
   ArraySetAsSeries(ma5, true);
   ArraySetAsSeries(fractal_up, true);
   ArraySetAsSeries(fractal_down, true);
   
   //--- Initialize daily tracking
   current_day = TimeCurrent();
   daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   Print("✅ 5MA Fractal Expert Advisor initialized successfully");
   Print("📊 Symbol: ", _Symbol, " | Timeframe: ", EnumToString(PERIOD_CURRENT));
   Print("💰 Risk per trade: ", RiskPercent, "% | Max daily loss: ", MaxDailyLoss, "%");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   IndicatorRelease(ma1_handle);
   IndicatorRelease(ma2_handle);
   IndicatorRelease(ma3_handle);
   IndicatorRelease(ma4_handle);
   IndicatorRelease(ma5_handle);
   IndicatorRelease(fractal_handle);
   
   Print("🔄 5MA Fractal EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check for new bar formation
   static datetime last_bar_time = 0;
   datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   
   if(current_bar_time == last_bar_time)
      return; // Wait for new bar
   last_bar_time = current_bar_time;
   
   //--- Check daily loss limit
   CheckDailyLoss();
   if(daily_loss >= MaxDailyLoss)
   {
      Print("⚠️ Daily loss limit reached: ", DoubleToString(daily_loss, 2), "%");
      return;
   }
   
   //--- Update all indicators
   if(!UpdateIndicators())
   {
      Print("❌ Failed to update indicators");
      return;
   }
   
   //--- Manage existing positions
   ManageOpenPositions();
   
   //--- Check for new trading opportunities
   if(CanOpenNewTrade())
   {
      CheckForBuySignal();
      CheckForSellSignal();
   }
}

//+------------------------------------------------------------------+
//| Update all indicator values                                      |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   //--- Copy Moving Average data
   if(CopyBuffer(ma1_handle, 0, 0, 5, ma1) <= 0) return false;
   if(CopyBuffer(ma2_handle, 0, 0, 5, ma2) <= 0) return false;
   if(CopyBuffer(ma3_handle, 0, 0, 5, ma3) <= 0) return false;
   if(CopyBuffer(ma4_handle, 0, 0, 5, ma4) <= 0) return false;
   if(CopyBuffer(ma5_handle, 0, 0, 5, ma5) <= 0) return false;
   
   //--- Copy Fractal data (need more bars for fractal detection)
   if(CopyBuffer(fractal_handle, 0, 0, 20, fractal_up) <= 0) return false;
   if(CopyBuffer(fractal_handle, 1, 0, 20, fractal_down) <= 0) return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Check daily loss and reset if new day                           |
//+------------------------------------------------------------------+
void CheckDailyLoss()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   datetime today = StructToTime(dt);
   
   //--- Reset daily tracking for new day
   if(today != current_day)
   {
      current_day = today;
      daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      daily_loss = 0;
      Print("📅 New trading day started. Balance: $", DoubleToString(daily_start_balance, 2));
   }
   
   //--- Calculate current daily loss
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(current_balance < daily_start_balance)
   {
      daily_loss = (daily_start_balance - current_balance) / daily_start_balance * 100.0;
   }
   else
   {
      daily_loss = 0; // Reset if we're in profit
   }
}

//+------------------------------------------------------------------+
//| Check if we can open a new trade                                |
//+------------------------------------------------------------------+
bool CanOpenNewTrade()
{
   //--- Check single trade limitation
   if(UseSingleTrade && CountOpenPositions() > 0)
      return false;
   
   //--- Check maximum trades limit
   if(CountOpenPositions() >= MaxOpenTrades)
      return false;
   
   //--- Check time since last trade (prevent overtrading)
   if(TimeCurrent() - last_trade_time < 300) // 5 minutes minimum
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Count positions opened by this EA                               |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Check for bullish trend alignment                               |
//+------------------------------------------------------------------+
bool IsBullishTrendAligned()
{
   if(!TrendFilterEnabled)
      return true;

   //--- Check MA alignment: EMA9 > EMA21 > SMA50 > SMA100 > SMA200
   return (ma1[0] > ma2[0] && ma2[0] > ma3[0] &&
           ma3[0] > ma4[0] && ma4[0] > ma5[0]);
}

//+------------------------------------------------------------------+
//| Check for bearish trend alignment                               |
//+------------------------------------------------------------------+
bool IsBearishTrendAligned()
{
   if(!TrendFilterEnabled)
      return true;

   //--- Check MA alignment: EMA9 < EMA21 < SMA50 < SMA100 < SMA200
   return (ma1[0] < ma2[0] && ma2[0] < ma3[0] &&
           ma3[0] < ma4[0] && ma4[0] < ma5[0]);
}

//+------------------------------------------------------------------+
//| Find recent fractal high or low                                 |
//+------------------------------------------------------------------+
double FindRecentFractal(bool find_high)
{
   for(int i = 2; i < 15; i++) // Start from index 2 (fractals need confirmation)
   {
      if(find_high)
      {
         if(fractal_up[i] != 0 && fractal_up[i] != EMPTY_VALUE)
            return fractal_up[i];
      }
      else
      {
         if(fractal_down[i] != 0 && fractal_down[i] != EMPTY_VALUE)
            return fractal_down[i];
      }
   }
   return 0; // No fractal found
}

//+------------------------------------------------------------------+
//| Check for buy signal                                            |
//+------------------------------------------------------------------+
void CheckForBuySignal()
{
   double ask_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double close_prev = iClose(_Symbol, PERIOD_CURRENT, 1);

   //--- 1. Check bullish trend alignment
   if(!IsBullishTrendAligned())
      return;

   //--- 2. Price must be above all MAs
   if(ask_price <= ma1[0] || ask_price <= ma2[0] ||
      ask_price <= ma3[0] || ask_price <= ma4[0] ||
      ask_price <= ma5[0])
      return;

   //--- 3. Find recent bullish fractal (swing low)
   double fractal_low = FindRecentFractal(false);
   if(fractal_low == 0)
      return;

   //--- 4. Entry condition: Previous candle closed above fractal and EMA9
   if(close_prev > fractal_low && close_prev > ma1[1])
   {
      //--- Calculate Stop Loss and Take Profit
      double sl_price, tp_price;

      if(UseFixedSLTP)
      {
         sl_price = ask_price - FixedSL * _Point * 10;
         tp_price = ask_price + FixedTP * _Point * 10;
      }
      else
      {
         //--- SL below recent bearish fractal (swing high)
         double fractal_high = FindRecentFractal(true);
         sl_price = (fractal_high > 0) ? fractal_high - 20 * _Point : ask_price - 50 * _Point * 10;

         //--- TP based on risk:reward ratio
         double sl_distance = ask_price - sl_price;
         tp_price = ask_price + (sl_distance * RiskReward);
      }

      //--- Execute buy trade
      ExecuteTrade(ORDER_TYPE_BUY, ask_price, sl_price, tp_price);
   }
}

//+------------------------------------------------------------------+
//| Check for sell signal                                           |
//+------------------------------------------------------------------+
void CheckForSellSignal()
{
   double bid_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double close_prev = iClose(_Symbol, PERIOD_CURRENT, 1);

   //--- 1. Check bearish trend alignment
   if(!IsBearishTrendAligned())
      return;

   //--- 2. Price must be below all MAs
   if(bid_price >= ma1[0] || bid_price >= ma2[0] ||
      bid_price >= ma3[0] || bid_price >= ma4[0] ||
      bid_price >= ma5[0])
      return;

   //--- 3. Find recent bearish fractal (swing high)
   double fractal_high = FindRecentFractal(true);
   if(fractal_high == 0)
      return;

   //--- 4. Entry condition: Previous candle closed below fractal and EMA9
   if(close_prev < fractal_high && close_prev < ma1[1])
   {
      //--- Calculate Stop Loss and Take Profit
      double sl_price, tp_price;

      if(UseFixedSLTP)
      {
         sl_price = bid_price + FixedSL * _Point * 10;
         tp_price = bid_price - FixedTP * _Point * 10;
      }
      else
      {
         //--- SL above recent bullish fractal (swing low)
         double fractal_low = FindRecentFractal(false);
         sl_price = (fractal_low > 0) ? fractal_low + 20 * _Point : bid_price + 50 * _Point * 10;

         //--- TP based on risk:reward ratio
         double sl_distance = sl_price - bid_price;
         tp_price = bid_price - (sl_distance * RiskReward);
      }

      //--- Execute sell trade
      ExecuteTrade(ORDER_TYPE_SELL, bid_price, sl_price, tp_price);
   }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size based on risk percentage             |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double sl_price)
{
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * RiskPercent / 100.0;

   double sl_distance = MathAbs(entry_price - sl_price);
   if(sl_distance == 0)
      return 0.01; // Minimum lot size

   //--- Calculate position size
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   double lot_size = risk_amount / (sl_distance / tick_size * tick_value);

   //--- Normalize lot size to broker requirements
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lot_size = MathMax(min_lot, MathMin(max_lot,
              MathRound(lot_size / lot_step) * lot_step));

   return lot_size;
}

//+------------------------------------------------------------------+
//| Execute trade with proper error handling                        |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type, double price, double sl, double tp)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   //--- Calculate lot size
   double lot_size = CalculateLotSize(price, sl);
   if(lot_size < SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
   {
      Print("❌ Calculated lot size too small: ", lot_size);
      return;
   }

   //--- Prepare trade request
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lot_size;
   request.type = order_type;
   request.price = price;
   request.sl = sl;
   request.tp = tp;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.deviation = 10;
   request.type_filling = ORDER_FILLING_FOK;

   //--- Send trade order
   if(OrderSend(request, result))
   {
      if(result.retcode == TRADE_RETCODE_DONE)
      {
         Print("✅ ", EnumToString(order_type), " trade opened successfully!");
         Print("📊 Price: ", DoubleToString(price, _Digits),
               " | SL: ", DoubleToString(sl, _Digits),
               " | TP: ", DoubleToString(tp, _Digits),
               " | Volume: ", DoubleToString(lot_size, 2));
         last_trade_time = TimeCurrent();
      }
      else
      {
         Print("⚠️ Trade executed with warning. Return code: ", result.retcode);
      }
   }
   else
   {
      Print("❌ Failed to open trade. Error code: ", result.retcode,
            " | Description: ", GetTradeErrorDescription(result.retcode));
   }
}

//+------------------------------------------------------------------+
//| Get trade error description                                     |
//+------------------------------------------------------------------+
string GetTradeErrorDescription(uint retcode)
{
   switch(retcode)
   {
      case TRADE_RETCODE_INVALID: return "Invalid request";
      case TRADE_RETCODE_INVALID_VOLUME: return "Invalid volume";
      case TRADE_RETCODE_INVALID_PRICE: return "Invalid price";
      case TRADE_RETCODE_INVALID_STOPS: return "Invalid stops";
      case TRADE_RETCODE_TRADE_DISABLED: return "Trade disabled";
      case TRADE_RETCODE_MARKET_CLOSED: return "Market closed";
      case TRADE_RETCODE_NO_MONEY: return "Not enough money";
      case TRADE_RETCODE_PRICE_CHANGED: return "Price changed";
      case TRADE_RETCODE_PRICE_OFF: return "Off quotes";
      case TRADE_RETCODE_INVALID_EXPIRATION: return "Invalid expiration";
      case TRADE_RETCODE_ORDER_CHANGED: return "Order changed";
      case TRADE_RETCODE_TOO_MANY_REQUESTS: return "Too many requests";
      case TRADE_RETCODE_NO_CHANGES: return "No changes";
      case TRADE_RETCODE_SERVER_DISABLES_AT: return "Autotrading disabled by server";
      case TRADE_RETCODE_CLIENT_DISABLES_AT: return "Autotrading disabled by client";
      case TRADE_RETCODE_LOCKED: return "Request locked";
      case TRADE_RETCODE_FROZEN: return "Order or position frozen";
      case TRADE_RETCODE_INVALID_FILL: return "Invalid fill";
      case TRADE_RETCODE_CONNECTION: return "No connection";
      case TRADE_RETCODE_ONLY_REAL: return "Only real accounts allowed";
      case TRADE_RETCODE_LIMIT_ORDERS: return "Limit orders limit reached";
      case TRADE_RETCODE_LIMIT_VOLUME: return "Volume limit reached";
      default: return "Unknown error";
   }
}

//+------------------------------------------------------------------+
//| Manage open positions (trailing stop, breakeven)               |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         {
            double position_profit = PositionGetDouble(POSITION_PROFIT);
            double position_open = PositionGetDouble(POSITION_PRICE_OPEN);
            double position_sl = PositionGetDouble(POSITION_SL);
            double position_tp = PositionGetDouble(POSITION_TP);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                   SymbolInfoDouble(_Symbol, SYMBOL_ASK);

            //--- Move to breakeven
            if(UseBreakeven && position_sl != position_open)
            {
               MoveToBreakeven(pos_type, position_open, current_price, position_sl);
            }

            //--- Apply trailing stop
            if(TrailingStopEnabled && position_profit > 0)
            {
               ApplyTrailingStop(pos_type, position_open, current_price, position_sl);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Move stop loss to breakeven                                     |
//+------------------------------------------------------------------+
void MoveToBreakeven(ENUM_POSITION_TYPE pos_type, double open_price, double current_price, double current_sl)
{
   double breakeven_distance = BreakevenPips * _Point * 10;
   bool should_move = false;

   if(pos_type == POSITION_TYPE_BUY)
   {
      should_move = (current_price >= open_price + breakeven_distance) &&
                    (current_sl < open_price);
   }
   else
   {
      should_move = (current_price <= open_price - breakeven_distance) &&
                    (current_sl > open_price);
   }

   if(should_move)
   {
      ModifyPosition(open_price, PositionGetDouble(POSITION_TP));
      Print("🎯 Position moved to breakeven at: ", DoubleToString(open_price, _Digits));
   }
}

//+------------------------------------------------------------------+
//| Apply trailing stop                                             |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ENUM_POSITION_TYPE pos_type, double open_price, double current_price, double current_sl)
{
   double trailing_distance = TrailingStep * _Point * 10;
   double min_profit = TrailingStart * _Point * 10;
   double new_sl = current_sl;
   bool should_modify = false;

   if(pos_type == POSITION_TYPE_BUY)
   {
      if(current_price >= open_price + min_profit)
      {
         new_sl = current_price - trailing_distance;
         should_modify = (new_sl > current_sl + _Point);
      }
   }
   else
   {
      if(current_price <= open_price - min_profit)
      {
         new_sl = current_price + trailing_distance;
         should_modify = (new_sl < current_sl - _Point);
      }
   }

   if(should_modify)
   {
      ModifyPosition(new_sl, PositionGetDouble(POSITION_TP));
      Print("📈 Trailing stop updated to: ", DoubleToString(new_sl, _Digits));
   }
}

//+------------------------------------------------------------------+
//| Modify position stop loss and take profit                       |
//+------------------------------------------------------------------+
void ModifyPosition(double new_sl, double new_tp)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.symbol = _Symbol;
   request.sl = new_sl;
   request.tp = new_tp;
   request.magic = MagicNumber;

   if(!OrderSend(request, result))
   {
      Print("❌ Failed to modify position. Error: ", result.retcode);
   }
}
